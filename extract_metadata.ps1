# PowerShell script to extract video metadata from 4K UHD collections
# Saves metadata to JSON file for fast compression analysis

param(
    [string[]]$Paths = @("F:\4K UHD", "N:\4K UHD"),
    [string]$OutputFile = "video_metadata_cache.json"
)

# MediaInfo CLI executable path
$MediaInfoPath = "C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\MediaArea.MediaInfo_Microsoft.Winget.Source_8wekyb3d8bbwe\MediaInfo.exe"

# Video file extensions to analyze
$VideoExtensions = @('.mkv', '.mp4', '.avi', '.m4v', '.mov', '.wmv', '.flv', '.webm', '.ts', '.m2ts')

Write-Host "Video Metadata Extraction" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Check if MediaInfo is available
if (-not (Test-Path $MediaInfoPath)) {
    Write-Host "Error: MediaInfo CLI not found at $MediaInfoPath" -ForegroundColor Red
    exit 1
}

# Initialize results
$metadataCache = @{
    extractionDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    pathsScanned = $Paths
    totalFiles = 0
    extractedFiles = 0
    failedFiles = @()
    videoFiles = @()
}

Write-Host "Extracting metadata for compression analysis..." -ForegroundColor Yellow

# Function to extract video metadata
function Extract-VideoMetadata {
    param($FilePath)
    
    try {
        Write-Host "Extracting: $([System.IO.Path]::GetFileName($FilePath))" -ForegroundColor Gray
        
        # Get basic file info
        $fileInfo = Get-Item $FilePath
        $fileSize = $fileInfo.Length
        $fileSizeGB = [math]::Round($fileSize / 1GB, 2)
        
        # Run MediaInfo with JSON output
        $mediaInfoOutput = & $MediaInfoPath --Output=JSON $FilePath
        
        if (-not $mediaInfoOutput) {
            Write-Warning "MediaInfo returned no output for $FilePath"
            return $null
        }
        
        # Parse JSON
        $mediaInfoJson = $mediaInfoOutput | ConvertFrom-Json
        
        if (-not $mediaInfoJson.media) {
            Write-Warning "Invalid MediaInfo JSON output for $FilePath"
            return $null
        }
        
        # Find video track
        $videoTrack = $mediaInfoJson.media.track | Where-Object { $_.'@type' -eq 'Video' } | Select-Object -First 1
        
        if (-not $videoTrack) {
            Write-Warning "No video track found in $FilePath"
            return $null
        }
        
        # Extract all relevant properties
        $metadata = @{
            # File information
            fileName = $fileInfo.Name
            fullPath = $FilePath
            directory = $fileInfo.Directory.FullName
            fileSizeBytes = $fileSize
            fileSizeGB = $fileSizeGB
            lastModified = $fileInfo.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss")
            
            # Video properties
            format = if ($videoTrack.Format) { $videoTrack.Format } else { "Unknown" }
            formatProfile = if ($videoTrack.Format_Profile) { $videoTrack.Format_Profile } else { "Unknown" }
            formatLevel = if ($videoTrack.Format_Level) { $videoTrack.Format_Level } else { "Unknown" }
            formatTier = if ($videoTrack.Format_Tier) { $videoTrack.Format_Tier } else { "Unknown" }
            
            # Encoding information
            encodedLibrary = if ($videoTrack.Encoded_Library) { $videoTrack.Encoded_Library } else { "Unknown" }
            encodedLibraryName = if ($videoTrack.Encoded_Library_Name) { $videoTrack.Encoded_Library_Name } else { "Unknown" }
            encodedLibraryVersion = if ($videoTrack.Encoded_Library_Version) { $videoTrack.Encoded_Library_Version } else { "Unknown" }
            encodedLibrarySettings = if ($videoTrack.Encoded_Library_Settings) { $videoTrack.Encoded_Library_Settings } else { "" }
            
            # Video specifications
            width = if ($videoTrack.Width) { [int]$videoTrack.Width } else { 0 }
            height = if ($videoTrack.Height) { [int]$videoTrack.Height } else { 0 }
            bitRate = if ($videoTrack.BitRate) { [long]$videoTrack.BitRate } else { 0 }
            bitRateKbps = if ($videoTrack.BitRate) { [int]($videoTrack.BitRate / 1000) } else { 0 }
            frameRate = if ($videoTrack.FrameRate) { [float]$videoTrack.FrameRate } else { 0 }
            frameRateMode = if ($videoTrack.FrameRate_Mode) { $videoTrack.FrameRate_Mode } else { "Unknown" }
            duration = if ($videoTrack.Duration) { [float]$videoTrack.Duration } else { 0 }
            bitDepth = if ($videoTrack.BitDepth) { [int]$videoTrack.BitDepth } else { 8 }
            
            # Color information
            colorSpace = if ($videoTrack.ColorSpace) { $videoTrack.ColorSpace } else { "Unknown" }
            chromaSubsampling = if ($videoTrack.ChromaSubsampling) { $videoTrack.ChromaSubsampling } else { "Unknown" }
            colorRange = if ($videoTrack.colour_range) { $videoTrack.colour_range } else { "Unknown" }
            colorPrimaries = if ($videoTrack.colour_primaries) { $videoTrack.colour_primaries } else { "Unknown" }
            transferCharacteristics = if ($videoTrack.transfer_characteristics) { $videoTrack.transfer_characteristics } else { "Unknown" }
            matrixCoefficients = if ($videoTrack.matrix_coefficients) { $videoTrack.matrix_coefficients } else { "Unknown" }
            
            # HDR information
            hdrFormat = if ($videoTrack.HDR_Format) { $videoTrack.HDR_Format } else { "None" }
            hdrFormatVersion = if ($videoTrack.HDR_Format_Version) { $videoTrack.HDR_Format_Version } else { "None" }
            hdrFormatProfile = if ($videoTrack.HDR_Format_Profile) { $videoTrack.HDR_Format_Profile } else { "None" }
            hdrFormatLevel = if ($videoTrack.HDR_Format_Level) { $videoTrack.HDR_Format_Level } else { "None" }
            hdrFormatSettings = if ($videoTrack.HDR_Format_Settings) { $videoTrack.HDR_Format_Settings } else { "None" }
            
            # Additional technical details
            scanType = if ($videoTrack.ScanType) { $videoTrack.ScanType } else { "Unknown" }
            scanOrder = if ($videoTrack.ScanOrder) { $videoTrack.ScanOrder } else { "Unknown" }
            pixelAspectRatio = if ($videoTrack.PixelAspectRatio) { [float]$videoTrack.PixelAspectRatio } else { 1.0 }
            displayAspectRatio = if ($videoTrack.DisplayAspectRatio) { [float]$videoTrack.DisplayAspectRatio } else { 0.0 }
            
            # Stream information
            streamSize = if ($videoTrack.StreamSize) { [long]$videoTrack.StreamSize } else { 0 }
            streamSizeGB = if ($videoTrack.StreamSize) { [math]::Round([long]$videoTrack.StreamSize / 1GB, 2) } else { 0 }
            
            # Calculated metrics
            resolution = "$(if ($videoTrack.Width) { $videoTrack.Width } else { 0 })x$(if ($videoTrack.Height) { $videoTrack.Height } else { 0 })"
            durationMinutes = if ($videoTrack.Duration) { [math]::Round([float]$videoTrack.Duration / 60, 1) } else { 0 }
            durationHours = if ($videoTrack.Duration) { [math]::Round([float]$videoTrack.Duration / 3600, 2) } else { 0 }
            pixelsPerFrame = if ($videoTrack.Width -and $videoTrack.Height) { [long]$videoTrack.Width * [long]$videoTrack.Height } else { 0 }
        }
        
        # Calculate additional derived metrics
        if ($metadata.pixelsPerFrame -gt 0 -and $metadata.frameRate -gt 0 -and $metadata.duration -gt 0) {
            $totalFrames = $metadata.frameRate * $metadata.duration
            $totalPixels = $metadata.pixelsPerFrame * $totalFrames
            $metadata.totalFrames = [long]$totalFrames
            $metadata.totalPixels = [long]$totalPixels
            $metadata.bitsPerPixel = if ($totalPixels -gt 0) { ($fileSize * 8) / $totalPixels } else { 0 }
        } else {
            $metadata.totalFrames = 0
            $metadata.totalPixels = 0
            $metadata.bitsPerPixel = 0
        }
        
        # Determine resolution category
        $metadata.resolutionCategory = if ($metadata.width -ge 3840) { 
            "4K UHD" 
        } elseif ($metadata.width -ge 2560) { 
            "1440p" 
        } elseif ($metadata.width -ge 1920) { 
            "1080p" 
        } elseif ($metadata.width -ge 1280) { 
            "720p" 
        } else { 
            "SD" 
        }
        
        # Check if likely remux (>40GB)
        $metadata.isLikelyRemux = $fileSizeGB -gt 40
        
        return $metadata
        
    } catch {
        Write-Warning "Error extracting metadata from $FilePath`: $($_.Exception.Message)"
        return $null
    }
}

# Scan each path
foreach ($path in $Paths) {
    if (-not (Test-Path $path)) {
        Write-Warning "Path not found: $path"
        continue
    }
    
    Write-Host "`nScanning: $path" -ForegroundColor Cyan
    
    # Get all video files recursively
    $videoFiles = Get-ChildItem -Path $path -Recurse -File | Where-Object { 
        $VideoExtensions -contains $_.Extension.ToLower() 
    }
    
    $metadataCache.totalFiles += $videoFiles.Count
    Write-Host "Found $($videoFiles.Count) video files" -ForegroundColor Yellow
    
    $fileCount = 0
    foreach ($file in $videoFiles) {
        $fileCount++
        Write-Host "[$fileCount/$($videoFiles.Count)] Processing: $($file.Name)" -ForegroundColor Gray
        
        try {
            # Extract metadata
            $metadata = Extract-VideoMetadata -FilePath $file.FullName
            
            if ($metadata) {
                $metadataCache.videoFiles += $metadata
                $metadataCache.extractedFiles++
                
                # Show file type
                if ($metadata.isLikelyRemux) {
                    Write-Host "  [REMUX] $($metadata.fileSizeGB) GB" -ForegroundColor Cyan
                } else {
                    Write-Host "  [VIDEO] $($metadata.fileSizeGB) GB - $($metadata.format) $($metadata.resolution)" -ForegroundColor Green
                }
            } else {
                $metadataCache.failedFiles += $file.FullName
            }
            
            # Save partial results every 25 files
            if ($fileCount % 25 -eq 0) {
                $metadataCache | ConvertTo-Json -Depth 10 | Out-File -FilePath "partial_$OutputFile" -Encoding UTF8
                Write-Host "    [SAVE] Partial metadata saved ($($metadataCache.extractedFiles) files)" -ForegroundColor Cyan
            }
            
        } catch {
            Write-Warning "Failed to process $($file.FullName): $($_.Exception.Message)"
            $metadataCache.failedFiles += $file.FullName
        }
    }
    
    # Save results after completing each directory
    $metadataCache | ConvertTo-Json -Depth 10 | Out-File -FilePath "partial_$OutputFile" -Encoding UTF8
    Write-Host "[SAVE] Directory completed - metadata saved" -ForegroundColor Green
}

# Save final results
$metadataCache | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputFile -Encoding UTF8

# Display summary
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "METADATA EXTRACTION COMPLETE" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green
Write-Host "Total files found: $($metadataCache.totalFiles)" -ForegroundColor White
Write-Host "Metadata extracted: $($metadataCache.extractedFiles)" -ForegroundColor Green
Write-Host "Failed extractions: $($metadataCache.failedFiles.Count)" -ForegroundColor Red

$remuxCount = ($metadataCache.videoFiles | Where-Object { $_.isLikelyRemux }).Count
$compressedCount = ($metadataCache.videoFiles | Where-Object { -not $_.isLikelyRemux }).Count

Write-Host "Likely remuxes (>40GB): $remuxCount" -ForegroundColor Cyan
Write-Host "Compressed files (≤40GB): $compressedCount" -ForegroundColor Yellow

$totalSizeGB = [math]::Round(($metadataCache.videoFiles | Measure-Object -Property fileSizeGB -Sum).Sum, 2)
Write-Host "Total collection size: $totalSizeGB GB" -ForegroundColor Cyan

Write-Host "`nMetadata cache saved to: $OutputFile" -ForegroundColor Green
Write-Host "Use this file for fast compression analysis!" -ForegroundColor Green
