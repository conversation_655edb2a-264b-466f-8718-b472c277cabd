# PowerShell script to analyze video compression quality in 4K UHD collections
# Identifies poorly compressed files that could be significantly smaller with similar quality

param(
    [string[]]$Paths = @("F:\4K UHD", "N:\4K UHD"),
    [string]$OutputFile = "compression_analysis.json"
)

# MediaInfo CLI executable path
$MediaInfoPath = "C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\MediaArea.MediaInfo_Microsoft.Winget.Source_8wekyb3d8bbwe\MediaInfo.exe"

# Video file extensions to analyze
$VideoExtensions = @('.mkv', '.mp4', '.avi', '.m4v', '.mov', '.wmv', '.flv', '.webm', '.ts', '.m2ts')

Write-Host "Video Compression Quality Analysis" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Check if MediaInfo is available
if (-not (Test-Path $MediaInfoPath)) {
    Write-Host "Error: MediaInfo not found at $MediaInfoPath" -ForegroundColor Red
    exit 1
}

# Initialize results
$results = @{
    scanDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    pathsScanned = $Paths
    totalFiles = 0
    analyzedFiles = 0
    poorlyCompressed = @()
    summary = @{
        totalSizeGB = 0
        potentialSavingsGB = 0
        averageBitrate = 0
        codecDistribution = @{}
    }
}

# Function to analyze video compression efficiency
function Test-CompressionEfficiency {
    param($FilePath, $MediaInfoJson)

    try {
        # Extract key metrics
        $fileSize = (Get-Item $FilePath).Length
        $fileSizeGB = [math]::Round($fileSize / 1GB, 2)

        # Find video track in JSON
        $videoTrack = $MediaInfoJson.media.track | Where-Object { $_.'@type' -eq 'Video' } | Select-Object -First 1

        if (-not $videoTrack) {
            Write-Warning "No video track found in $FilePath"
            return $null
        }

        # Extract specific values from JSON
        $bitrate = if ($videoTrack.BitRate) { [int]($videoTrack.BitRate / 1000) } else { 0 }  # Convert to kbps
        $width = if ($videoTrack.Width) { [int]$videoTrack.Width } else { 0 }
        $height = if ($videoTrack.Height) { [int]$videoTrack.Height } else { 0 }
        $duration = if ($videoTrack.Duration) { [float]$videoTrack.Duration * 1000 } else { 0 }  # Convert to ms
        $codec = if ($videoTrack.Format) { $videoTrack.Format } else { "Unknown" }
        $frameRate = if ($videoTrack.FrameRate) { [float]$videoTrack.FrameRate } else { 0 }
        
        # Calculate compression efficiency metrics
        $pixelsPerFrame = $width * $height
        $totalPixels = $pixelsPerFrame * $frameRate * ($duration / 1000)
        $bitsPerPixel = if ($totalPixels -gt 0) { ($fileSize * 8) / $totalPixels } else { 0 }
        
        # Determine if compression is poor based on various factors
        $isPoorlyCompressed = $false
        $reasons = @()
        $potentialSavings = 0
        
        # Rule 1: Very high bitrate for resolution (conservative thresholds)
        $expectedBitrate = switch ($true) {
            ($width -ge 3840) { 25000 }  # 4K: ~25 Mbps max reasonable
            ($width -ge 1920) { 8000 }   # 1080p: ~8 Mbps max reasonable
            ($width -ge 1280) { 4000 }   # 720p: ~4 Mbps max reasonable
            default { 2000 }
        }
        
        if ($bitrate -gt $expectedBitrate * 1.5) {
            $isPoorlyCompressed = $true
            $reasons += "Excessive bitrate: $bitrate kbps (expected max: $expectedBitrate kbps)"
            $potentialSavings += $fileSizeGB * 0.3  # Estimate 30% savings
        }
        
        # Rule 2: Very high bits per pixel
        $maxBitsPerPixel = switch ($codec.ToLower()) {
            { $_ -match "hevc|h\.265" } { 0.15 }
            { $_ -match "avc|h\.264" } { 0.25 }
            { $_ -match "av1" } { 0.12 }
            default { 0.3 }
        }
        
        if ($bitsPerPixel -gt $maxBitsPerPixel) {
            $isPoorlyCompressed = $true
            $reasons += "High bits per pixel: $([math]::Round($bitsPerPixel, 4)) (max efficient: $maxBitsPerPixel)"
            $potentialSavings += $fileSizeGB * 0.25  # Estimate 25% savings
        }
        
        # Rule 3: Large file size relative to duration and resolution
        $durationHours = $duration / 3600000
        $expectedSizeGB = switch ($true) {
            ($width -ge 3840) { $durationHours * 12 }  # 4K: ~12GB per hour max
            ($width -ge 1920) { $durationHours * 4 }   # 1080p: ~4GB per hour max
            default { $durationHours * 2 }
        }
        
        if ($fileSizeGB -gt $expectedSizeGB * 1.8) {
            $isPoorlyCompressed = $true
            $reasons += "Large file size: $fileSizeGB GB (expected max: $([math]::Round($expectedSizeGB, 1)) GB)"
            $potentialSavings += $fileSizeGB * 0.4  # Estimate 40% savings
        }
        
        # Rule 4: Old codec with large file size
        if ($codec -match "MPEG-2|DivX|XviD" -and $fileSizeGB -gt 2) {
            $isPoorlyCompressed = $true
            $reasons += "Outdated codec: $codec with large file size"
            $potentialSavings += $fileSizeGB * 0.6  # Estimate 60% savings with modern codec
        }
        
        return @{
            isPoorlyCompressed = $isPoorlyCompressed
            reasons = $reasons
            potentialSavingsGB = [math]::Round($potentialSavings, 2)
            metrics = @{
                codec = $codec
                bitrate = $bitrate
                resolution = "${width}x${height}"
                frameRate = $frameRate
                durationMinutes = [math]::Round($duration / 60000, 1)
                fileSizeGB = $fileSizeGB
                bitsPerPixel = [math]::Round($bitsPerPixel, 4)
            }
        }
    }
    catch {
        Write-Warning "Error analyzing $FilePath`: $($_.Exception.Message)"
        return $null
    }
}

# Scan each path
foreach ($path in $Paths) {
    if (-not (Test-Path $path)) {
        Write-Warning "Path not found: $path"
        continue
    }
    
    Write-Host "`nScanning: $path" -ForegroundColor Cyan
    
    # Get all video files recursively
    $videoFiles = Get-ChildItem -Path $path -Recurse -File | Where-Object { 
        $VideoExtensions -contains $_.Extension.ToLower() 
    }
    
    $results.totalFiles += $videoFiles.Count
    Write-Host "Found $($videoFiles.Count) video files" -ForegroundColor Yellow
    
    foreach ($file in $videoFiles) {
        Write-Host "Analyzing: $($file.Name)" -ForegroundColor Gray
        
        try {
            # Run MediaInfo with JSON output
            $mediaInfoJson = & $MediaInfoPath --Output=JSON $file.FullName | ConvertFrom-Json

            if ($mediaInfoJson -and $mediaInfoJson.media) {
                $results.analyzedFiles++

                # Analyze compression efficiency
                $analysis = Test-CompressionEfficiency -FilePath $file.FullName -MediaInfoJson $mediaInfoJson
                
                if ($analysis -and $analysis.isPoorlyCompressed) {
                    $results.poorlyCompressed += @{
                        fileName = $file.Name
                        fullPath = $file.FullName
                        directory = $file.Directory.FullName
                        reasons = $analysis.reasons
                        potentialSavingsGB = $analysis.potentialSavingsGB
                        metrics = $analysis.metrics
                    }
                    
                    Write-Host "  ⚠️  Poorly compressed!" -ForegroundColor Red
                }
                
                # Update summary statistics
                $results.summary.totalSizeGB += $analysis.metrics.fileSizeGB
                $results.summary.potentialSavingsGB += $analysis.potentialSavingsGB
                
                # Track codec distribution
                $codec = $analysis.metrics.codec
                if ($results.summary.codecDistribution.ContainsKey($codec)) {
                    $results.summary.codecDistribution[$codec]++
                } else {
                    $results.summary.codecDistribution[$codec] = 1
                }
            }
        }
        catch {
            Write-Warning "Failed to analyze $($file.FullName): $($_.Exception.Message)"
        }
    }
}

# Finalize summary
$results.summary.totalSizeGB = [math]::Round($results.summary.totalSizeGB, 2)
$results.summary.potentialSavingsGB = [math]::Round($results.summary.potentialSavingsGB, 2)
$results.summary.averageBitrate = if ($results.analyzedFiles -gt 0) { 
    [math]::Round(($results.poorlyCompressed | ForEach-Object { $_.metrics.bitrate } | Measure-Object -Average).Average, 0)
} else { 0 }

# Export results
$results | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputFile -Encoding UTF8

# Display summary
Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "COMPRESSION ANALYSIS COMPLETE" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green
Write-Host "Total files found: $($results.totalFiles)" -ForegroundColor White
Write-Host "Files analyzed: $($results.analyzedFiles)" -ForegroundColor White
Write-Host "Poorly compressed files: $($results.poorlyCompressed.Count)" -ForegroundColor Red
Write-Host "Total collection size: $($results.summary.totalSizeGB) GB" -ForegroundColor Cyan
Write-Host "Potential savings: $($results.summary.potentialSavingsGB) GB" -ForegroundColor Yellow
$savingsPercentage = if ($results.summary.totalSizeGB -gt 0) { [math]::Round(($results.summary.potentialSavingsGB / $results.summary.totalSizeGB) * 100, 1) } else { 0 }
Write-Host "Potential space savings: $savingsPercentage%" -ForegroundColor Yellow

if ($results.poorlyCompressed.Count -gt 0) {
    Write-Host "`nTop poorly compressed files:" -ForegroundColor Red
    $results.poorlyCompressed | Sort-Object potentialSavingsGB -Descending | Select-Object -First 10 | ForEach-Object {
        Write-Host "  • $($_.fileName) - Potential savings: $($_.potentialSavingsGB) GB" -ForegroundColor White
        $_.reasons | ForEach-Object { Write-Host "    - $_" -ForegroundColor Gray }
    }
}

Write-Host "`nDetailed results saved to: $OutputFile" -ForegroundColor Green
