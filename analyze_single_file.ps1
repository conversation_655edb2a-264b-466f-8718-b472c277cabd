# PowerShell script to analyze compression quality of a single video file
# Usage: .\analyze_single_file.ps1 -FilePath "path\to\video.mkv"

param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath
)

# MediaInfo CLI executable path
$MediaInfoPath = "C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\MediaArea.MediaInfo_Microsoft.Winget.Source_8wekyb3d8bbwe\MediaInfo.exe"

Write-Host "Single File Video Compression Analysis" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Check if MediaInfo is available
if (-not (Test-Path $MediaInfoPath)) {
    Write-Host "Error: MediaInfo CLI not found at $MediaInfoPath" -ForegroundColor Red
    exit 1
}

# Check if file exists
if (-not (Test-Path $FilePath)) {
    Write-Host "Error: File not found: $FilePath" -ForegroundColor Red
    exit 1
}

Write-Host "Analyzing file: $FilePath" -ForegroundColor Cyan

try {
    # Get basic file info
    $fileInfo = Get-Item $FilePath
    $fileSize = $fileInfo.Length
    $fileSizeGB = [math]::Round($fileSize / 1GB, 2)
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    
    Write-Host "File size: $fileSizeGB GB ($fileSizeMB MB)" -ForegroundColor Yellow
    
    # Run MediaInfo with JSON output
    Write-Host "Running MediaInfo analysis..." -ForegroundColor Gray
    $mediaInfoOutput = & $MediaInfoPath --Output=JSON $FilePath
    
    if (-not $mediaInfoOutput) {
        Write-Host "Error: MediaInfo returned no output" -ForegroundColor Red
        exit 1
    }
    
    # Parse JSON
    $mediaInfoJson = $mediaInfoOutput | ConvertFrom-Json
    
    if (-not $mediaInfoJson.media) {
        Write-Host "Error: Invalid MediaInfo JSON output" -ForegroundColor Red
        exit 1
    }
    
    # Find video track
    $videoTrack = $mediaInfoJson.media.track | Where-Object { $_.'@type' -eq 'Video' } | Select-Object -First 1
    
    if (-not $videoTrack) {
        Write-Host "Error: No video track found in file" -ForegroundColor Red
        exit 1
    }
    
    # Extract video properties
    $codec = if ($videoTrack.Format) { $videoTrack.Format } else { "Unknown" }
    $profile = if ($videoTrack.Format_Profile) { $videoTrack.Format_Profile } else { "Unknown" }
    $bitrate = if ($videoTrack.BitRate) { [int]($videoTrack.BitRate / 1000) } else { 0 }  # Convert to kbps
    $width = if ($videoTrack.Width) { [int]$videoTrack.Width } else { 0 }
    $height = if ($videoTrack.Height) { [int]$videoTrack.Height } else { 0 }
    $frameRate = if ($videoTrack.FrameRate) { [float]$videoTrack.FrameRate } else { 0 }
    $duration = if ($videoTrack.Duration) { [float]$videoTrack.Duration } else { 0 }  # in seconds
    $bitDepth = if ($videoTrack.BitDepth) { [int]$videoTrack.BitDepth } else { 8 }
    
    # Calculate derived metrics
    $resolution = "${width}x${height}"
    $durationMinutes = [math]::Round($duration / 60, 1)
    $durationHours = [math]::Round($duration / 3600, 2)
    $pixelsPerFrame = $width * $height
    $totalFrames = if ($frameRate -gt 0) { $duration * $frameRate } else { 0 }
    $totalPixels = $pixelsPerFrame * $totalFrames
    $bitsPerPixel = if ($totalPixels -gt 0) { ($fileSize * 8) / $totalPixels } else { 0 }
    
    # Display basic info
    Write-Host "`n" + "="*50 -ForegroundColor Cyan
    Write-Host "VIDEO PROPERTIES" -ForegroundColor Cyan
    Write-Host "="*50 -ForegroundColor Cyan
    Write-Host "Codec: $codec" -ForegroundColor White
    Write-Host "Profile: $profile" -ForegroundColor White
    Write-Host "Resolution: $resolution" -ForegroundColor White
    Write-Host "Bit Depth: $bitDepth bit" -ForegroundColor White
    Write-Host "Frame Rate: $frameRate fps" -ForegroundColor White
    Write-Host "Duration: $durationMinutes minutes ($durationHours hours)" -ForegroundColor White
    Write-Host "Bitrate: $bitrate kbps" -ForegroundColor White
    Write-Host "Bits per pixel: $([math]::Round($bitsPerPixel, 4))" -ForegroundColor White
    
    # Compression efficiency analysis
    Write-Host "`n" + "="*50 -ForegroundColor Yellow
    Write-Host "COMPRESSION ANALYSIS" -ForegroundColor Yellow
    Write-Host "="*50 -ForegroundColor Yellow
    
    $issues = @()
    $potentialSavings = 0
    
    # Determine resolution category
    $resolutionCategory = if ($width -ge 3840) {
        "4K UHD"
    } elseif ($width -ge 2560) {
        "1440p"
    } elseif ($width -ge 1920) {
        "1080p"
    } elseif ($width -ge 1280) {
        "720p"
    } else {
        "SD"
    }

    Write-Host "Resolution Category: $resolutionCategory" -ForegroundColor Cyan

    # Expected bitrate ranges (conservative estimates)
    $expectedBitrates = @{
        "4K UHD" = @{ min = 15000; max = 25000; optimal = 20000 }
        "1440p" = @{ min = 6000; max = 12000; optimal = 8000 }
        "1080p" = @{ min = 3000; max = 8000; optimal = 5000 }
        "720p" = @{ min = 1500; max = 4000; optimal = 2500 }
        "SD" = @{ min = 500; max = 2000; optimal = 1000 }
    }

    $expected = $expectedBitrates[$resolutionCategory]
    Write-Host "Expected bitrate range: $($expected.min) - $($expected.max) kbps (optimal: $($expected.optimal) kbps)" -ForegroundColor Gray
    
    # Rule 1: Bitrate analysis
    if ($bitrate -gt $expected.max * 1.5) {
        $issues += "⚠️  EXCESSIVE BITRATE: $bitrate kbps (expected max: $($expected.max) kbps)"
        $potentialSavings += $fileSizeGB * 0.3
        Write-Host "Status: POORLY COMPRESSED - Excessive bitrate" -ForegroundColor Red
    }
    elseif ($bitrate -gt $expected.max) {
        $issues += "⚠️  HIGH BITRATE: $bitrate kbps (recommended max: $($expected.max) kbps)"
        $potentialSavings += $fileSizeGB * 0.15
        Write-Host "Status: SUBOPTIMAL - High bitrate" -ForegroundColor Yellow
    }
    elseif ($bitrate -lt $expected.min) {
        Write-Host "Status: POTENTIALLY UNDERCOMPRESSED - Very low bitrate" -ForegroundColor Yellow
    }
    else {
        Write-Host "Status: BITRATE OK" -ForegroundColor Green
    }
    
    # Rule 2: Bits per pixel analysis
    $maxBitsPerPixel = switch ($codec.ToLower()) {
        { $_ -match "hevc|h\.265" } { 0.15 }
        { $_ -match "avc|h\.264" } { 0.25 }
        { $_ -match "av1" } { 0.12 }
        { $_ -match "vp9" } { 0.18 }
        default { 0.3 }
    }
    
    Write-Host "Bits per pixel: $([math]::Round($bitsPerPixel, 4)) (max efficient for $codec`: $maxBitsPerPixel)" -ForegroundColor Gray
    
    if ($bitsPerPixel -gt $maxBitsPerPixel * 1.5) {
        $issues += "⚠️  VERY HIGH bits per pixel: $([math]::Round($bitsPerPixel, 4)) (efficient max: $maxBitsPerPixel)"
        $potentialSavings += $fileSizeGB * 0.25
    }
    elseif ($bitsPerPixel -gt $maxBitsPerPixel) {
        $issues += "⚠️  HIGH bits per pixel: $([math]::Round($bitsPerPixel, 4)) (recommended max: $maxBitsPerPixel)"
        $potentialSavings += $fileSizeGB * 0.1
    }
    
    # Rule 3: File size vs duration analysis
    $expectedSizePerHour = switch ($resolutionCategory) {
        "4K UHD" { 12 }  # GB per hour
        "1440p" { 5 }
        "1080p" { 4 }
        "720p" { 2 }
        "SD" { 1 }
    }
    
    $expectedTotalSize = $durationHours * $expectedSizePerHour
    Write-Host "Expected file size: $([math]::Round($expectedTotalSize, 1)) GB (based on $expectedSizePerHour GB/hour for $resolutionCategory)" -ForegroundColor Gray
    
    if ($fileSizeGB -gt $expectedTotalSize * 2) {
        $issues += "⚠️  VERY LARGE file size: $fileSizeGB GB (expected: ~$([math]::Round($expectedTotalSize, 1)) GB)"
        $potentialSavings += $fileSizeGB * 0.4
    }
    elseif ($fileSizeGB -gt $expectedTotalSize * 1.5) {
        $issues += "⚠️  LARGE file size: $fileSizeGB GB (expected: ~$([math]::Round($expectedTotalSize, 1)) GB)"
        $potentialSavings += $fileSizeGB * 0.2
    }
    
    # Rule 4: Codec efficiency
    if ($codec -match "MPEG-2|DivX|XviD" -and $fileSizeGB -gt 2) {
        $issues += "⚠️  OUTDATED CODEC: $codec - could benefit from modern codec (H.264/H.265/AV1)"
        $potentialSavings += $fileSizeGB * 0.6
    }
    
    # Summary
    Write-Host "`n" + "="*50 -ForegroundColor Magenta
    Write-Host "COMPRESSION SUMMARY" -ForegroundColor Magenta
    Write-Host "="*50 -ForegroundColor Magenta
    
    if ($issues.Count -eq 0) {
        Write-Host "✅ COMPRESSION LOOKS GOOD!" -ForegroundColor Green
        Write-Host "This file appears to be reasonably well compressed." -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  COMPRESSION ISSUES DETECTED:" -ForegroundColor Red
        foreach ($issue in $issues) {
            Write-Host "   $issue" -ForegroundColor White
        }
        
        $potentialSavings = [math]::Round($potentialSavings, 2)
        if ($potentialSavings -gt 0) {
            $savingsPercent = [math]::Round(($potentialSavings / $fileSizeGB) * 100, 1)
            Write-Host "`nPotential savings: $potentialSavings GB ($savingsPercent% reduction)" -ForegroundColor Yellow
            
            # Recommendations
            Write-Host "`nRECOMMENDATIONS:" -ForegroundColor Cyan
            if ($codec -notmatch "hevc|h\.265|av1") {
                Write-Host "• Consider re-encoding with H.265 (HEVC) or AV1 for better compression" -ForegroundColor White
            }
            if ($bitrate -gt $expected.optimal * 1.2) {
                Write-Host "• Reduce bitrate to around $($expected.optimal) kbps for optimal quality/size balance" -ForegroundColor White
            }
            Write-Host "• Use 2-pass encoding for better quality at lower bitrates" -ForegroundColor White
            Write-Host "• Consider using CRF (Constant Rate Factor) instead of fixed bitrate" -ForegroundColor White
        }
    }
    
    Write-Host "`nAnalysis complete!" -ForegroundColor Green
    
} catch {
    Write-Host "Error during analysis: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Gray
    exit 1
}
