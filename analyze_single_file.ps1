# PowerShell script to analyze compression quality of a single video file
# Usage: .\analyze_single_file.ps1 -FilePath "path\to\video.mkv"

param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath
)

# MediaInfo CLI executable path
$MediaInfoPath = "C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\MediaArea.MediaInfo_Microsoft.Winget.Source_8wekyb3d8bbwe\MediaInfo.exe"

Write-Host "Single File Video Compression Analysis" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Check if MediaInfo is available
if (-not (Test-Path $MediaInfoPath)) {
    Write-Host "Error: MediaInfo CLI not found at $MediaInfoPath" -ForegroundColor Red
    exit 1
}

# Check if file exists
if (-not (Test-Path $FilePath)) {
    Write-Host "Error: File not found: $FilePath" -ForegroundColor Red
    exit 1
}

Write-Host "Analyzing file: $FilePath" -ForegroundColor Cyan

try {
    # Get basic file info
    $fileInfo = Get-Item $FilePath
    $fileSize = $fileInfo.Length
    $fileSizeGB = [math]::Round($fileSize / 1GB, 2)
    $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
    
    Write-Host "File size: $fileSizeGB GB ($fileSizeMB MB)" -ForegroundColor Yellow
    
    # Run MediaInfo with JSON output
    Write-Host "Running MediaInfo analysis..." -ForegroundColor Gray
    $mediaInfoOutput = & $MediaInfoPath --Output=JSON $FilePath
    
    if (-not $mediaInfoOutput) {
        Write-Host "Error: MediaInfo returned no output" -ForegroundColor Red
        exit 1
    }
    
    # Parse JSON
    $mediaInfoJson = $mediaInfoOutput | ConvertFrom-Json
    
    if (-not $mediaInfoJson.media) {
        Write-Host "Error: Invalid MediaInfo JSON output" -ForegroundColor Red
        exit 1
    }
    
    # Find video track
    $videoTrack = $mediaInfoJson.media.track | Where-Object { $_.'@type' -eq 'Video' } | Select-Object -First 1
    
    if (-not $videoTrack) {
        Write-Host "Error: No video track found in file" -ForegroundColor Red
        exit 1
    }
    
    # Extract video properties
    $codec = if ($videoTrack.Format) { $videoTrack.Format } else { "Unknown" }
    $profile = if ($videoTrack.Format_Profile) { $videoTrack.Format_Profile } else { "Unknown" }
    $bitrate = if ($videoTrack.BitRate) { [int]($videoTrack.BitRate / 1000) } else { 0 }  # Convert to kbps
    $width = if ($videoTrack.Width) { [int]$videoTrack.Width } else { 0 }
    $height = if ($videoTrack.Height) { [int]$videoTrack.Height } else { 0 }
    $frameRate = if ($videoTrack.FrameRate) { [float]$videoTrack.FrameRate } else { 0 }
    $duration = if ($videoTrack.Duration) { [float]$videoTrack.Duration } else { 0 }  # in seconds
    $bitDepth = if ($videoTrack.BitDepth) { [int]$videoTrack.BitDepth } else { 8 }
    
    # Calculate derived metrics
    $resolution = "${width}x${height}"
    $durationMinutes = [math]::Round($duration / 60, 1)
    $durationHours = [math]::Round($duration / 3600, 2)
    $pixelsPerFrame = $width * $height
    $totalFrames = if ($frameRate -gt 0) { $duration * $frameRate } else { 0 }
    $totalPixels = $pixelsPerFrame * $totalFrames
    $bitsPerPixel = if ($totalPixels -gt 0) { ($fileSize * 8) / $totalPixels } else { 0 }
    
    # Display basic info
    Write-Host "`n" + "="*50 -ForegroundColor Cyan
    Write-Host "VIDEO PROPERTIES" -ForegroundColor Cyan
    Write-Host "="*50 -ForegroundColor Cyan
    Write-Host "Codec: $codec" -ForegroundColor White
    Write-Host "Profile: $profile" -ForegroundColor White
    Write-Host "Resolution: $resolution" -ForegroundColor White
    Write-Host "Bit Depth: $bitDepth bit" -ForegroundColor White
    Write-Host "Frame Rate: $frameRate fps" -ForegroundColor White
    Write-Host "Duration: $durationMinutes minutes ($durationHours hours)" -ForegroundColor White
    Write-Host "Bitrate: $bitrate kbps" -ForegroundColor White
    Write-Host "Bits per pixel: $([math]::Round($bitsPerPixel, 4))" -ForegroundColor White
    
    # Compression efficiency analysis
    Write-Host "`n" + "="*50 -ForegroundColor Yellow
    Write-Host "COMPRESSION ANALYSIS" -ForegroundColor Yellow
    Write-Host "="*50 -ForegroundColor Yellow
    
    $issues = @()
    $potentialSavings = 0
    
    # Determine resolution category
    $resolutionCategory = if ($width -ge 3840) {
        "4K UHD"
    } elseif ($width -ge 2560) {
        "1440p"
    } elseif ($width -ge 1920) {
        "1080p"
    } elseif ($width -ge 1280) {
        "720p"
    } else {
        "SD"
    }

    Write-Host "Resolution Category: $resolutionCategory" -ForegroundColor Cyan

    # Expected bitrate ranges (conservative estimates)
    $expectedBitrates = @{
        "4K UHD" = @{ min = 15000; max = 25000; optimal = 20000 }
        "1440p" = @{ min = 6000; max = 12000; optimal = 8000 }
        "1080p" = @{ min = 3000; max = 8000; optimal = 5000 }
        "720p" = @{ min = 1500; max = 4000; optimal = 2500 }
        "SD" = @{ min = 500; max = 2000; optimal = 1000 }
    }

    $expected = $expectedBitrates[$resolutionCategory]
    Write-Host "Expected bitrate range: $($expected.min) - $($expected.max) kbps (optimal: $($expected.optimal) kbps)" -ForegroundColor Gray
    
    # Rule 1: Bitrate analysis
    if ($bitrate -gt $expected.max * 1.5) {
        $issues += "⚠️  EXCESSIVE BITRATE: $bitrate kbps (expected max: $($expected.max) kbps)"
        $potentialSavings += $fileSizeGB * 0.3
        Write-Host "Status: POORLY COMPRESSED - Excessive bitrate" -ForegroundColor Red
    }
    elseif ($bitrate -gt $expected.max) {
        $issues += "⚠️  HIGH BITRATE: $bitrate kbps (recommended max: $($expected.max) kbps)"
        $potentialSavings += $fileSizeGB * 0.15
        Write-Host "Status: SUBOPTIMAL - High bitrate" -ForegroundColor Yellow
    }
    elseif ($bitrate -lt $expected.min) {
        Write-Host "Status: POTENTIALLY UNDERCOMPRESSED - Very low bitrate" -ForegroundColor Yellow
    }
    else {
        Write-Host "Status: BITRATE OK" -ForegroundColor Green
    }
    
    # Rule 2: Advanced x265/HEVC compression efficiency analysis
    $encodingLibrary = if ($videoTrack.Encoded_Library) { $videoTrack.Encoded_Library } else { "Unknown" }
    $encodingSettings = if ($videoTrack.Encoded_Library_Settings) { $videoTrack.Encoded_Library_Settings } else { "" }

    Write-Host "Encoding Library: $encodingLibrary" -ForegroundColor Gray
    Write-Host "Encoding Settings Available: $($encodingSettings.Length -gt 0)" -ForegroundColor Gray
    Write-Host "Contains x265: $($encodingSettings -match 'x265')" -ForegroundColor Gray

    # Analyze x265 settings if available
    $x265Issues = @()
    $x265Savings = 0

    if ($encodingLibrary -match "x265" -and $encodingSettings.Length -gt 0) {
        Write-Host "Analyzing x265 encoding settings..." -ForegroundColor Gray
        Write-Host "Settings string length: $($encodingSettings.Length)" -ForegroundColor Gray

        # Parse x265 settings (handle both : and / separators)
        $settings = @{}
        $settingsParts = $encodingSettings -split '[:/]' | Where-Object { $_.Trim().Length -gt 0 }

        foreach ($part in $settingsParts) {
            $part = $part.Trim()
            if ($part -match '^([^=\s]+)=(.+)$') {
                $key = $matches[1].Trim()
                $value = $matches[2].Trim()
                $settings[$key] = $value
            } elseif ($part -match '^(no-.+)$' -or $part -match '^([a-zA-Z-]+)$') {
                $key = $matches[1].Trim()
                $settings[$key] = $true
            }
        }

        # Check for inefficient settings based on your analysis
        Write-Host "Found settings: ref=$($settings['ref']), me=$($settings['me']), subme=$($settings['subme'])" -ForegroundColor Gray

        # 1. Reference frames
        if ($settings.ContainsKey('ref') -and [int]$settings['ref'] -lt 3) {
            $x265Issues += "LOW reference frames: ref=$($settings['ref']) (efficient: ref=4)"
            $x265Savings += $fileSizeGB * 0.15
        }

        # 2. Motion estimation
        if ($settings.ContainsKey('me') -and [int]$settings['me'] -eq 0) {
            $x265Issues += "NO motion estimation: me=0 (efficient: me=2-3)"
            $x265Savings += $fileSizeGB * 0.25
        }
        if ($settings.ContainsKey('subme') -and [int]$settings['subme'] -eq 0) {
            $x265Issues += "NO subpixel motion estimation: subme=0 (efficient: subme=3-5)"
            $x265Savings += $fileSizeGB * 0.20
        }

        # 3. B-frames
        if ($settings.ContainsKey('b-adapt') -and [int]$settings['b-adapt'] -eq 0) {
            $x265Issues += "NO adaptive B-frames: b-adapt=0 (efficient: b-adapt=2)"
            $x265Savings += $fileSizeGB * 0.10
        }

        # 4. Rate control lookahead
        if ($settings.ContainsKey('rc-lookahead') -and [int]$settings['rc-lookahead'] -lt 20) {
            $x265Issues += "SHORT lookahead: rc-lookahead=$($settings['rc-lookahead']) (efficient: 40-60)"
            $x265Savings += $fileSizeGB * 0.08
        }

        # 5. Scene cut detection
        if ($settings.ContainsKey('scenecut') -and [int]$settings['scenecut'] -eq 0) {
            $x265Issues += "SCENE CUT disabled: scenecut=0 (efficient: scenecut=40)"
            $x265Savings += $fileSizeGB * 0.12
        }

        # 6. Adaptive quantization
        if ($settings.ContainsKey('aq-strength') -and [float]$settings['aq-strength'] -eq 0.0) {
            $x265Issues += "NO adaptive quantization: aq-strength=0.00 (efficient: aq-strength=0.8-1.2)"
            $x265Savings += $fileSizeGB * 0.15
        }

        # 7. SAO filter
        if ($settings.ContainsKey('no-sao')) {
            $x265Issues += "SAO DISABLED: no-sao (efficient: enable sao)"
            $x265Savings += $fileSizeGB * 0.05
        }

        # 8. Rate control mode
        if ($settings.ContainsKey('rc') -and $settings['rc'] -eq 'abr') {
            $x265Issues += "ABR rate control: less efficient than CRF (efficient: crf=17-22 for 4K HDR)"
            $x265Savings += $fileSizeGB * 0.20
        }

        # 9. CTU size
        if ($settings.ContainsKey('ctu') -and [int]$settings['ctu'] -lt 64 -and $width -ge 3840) {
            $x265Issues += "SMALL CTU size: ctu=$($settings['ctu']) (efficient for 4K: ctu=64)"
            $x265Savings += $fileSizeGB * 0.05
        }

        # 10. Psychovisual settings
        if ($settings.ContainsKey('psy-rdoq') -and [float]$settings['psy-rdoq'] -gt 3.0) {
            $x265Issues += "EXCESSIVE psy-rdoq: psy-rdoq=$($settings['psy-rdoq']) (efficient: 1.0-2.0)"
            $x265Savings += $fileSizeGB * 0.08
        }
    }

    # Standard bits per pixel analysis
    $maxBitsPerPixel = switch ($codec.ToLower()) {
        { $_ -match "hevc|h\.265" } { 0.15 }
        { $_ -match "avc|h\.264" } { 0.25 }
        { $_ -match "av1" } { 0.12 }
        { $_ -match "vp9" } { 0.18 }
        default { 0.3 }
    }

    Write-Host "Bits per pixel: $([math]::Round($bitsPerPixel, 4)) (max efficient for $codec`: $maxBitsPerPixel)" -ForegroundColor Gray

    if ($bitsPerPixel -gt $maxBitsPerPixel * 1.5) {
        $issues += "⚠️  VERY HIGH bits per pixel: $([math]::Round($bitsPerPixel, 4)) (efficient max: $maxBitsPerPixel)"
        $potentialSavings += $fileSizeGB * 0.25
    }
    elseif ($bitsPerPixel -gt $maxBitsPerPixel) {
        $issues += "⚠️  HIGH bits per pixel: $([math]::Round($bitsPerPixel, 4)) (recommended max: $maxBitsPerPixel)"
        $potentialSavings += $fileSizeGB * 0.1
    }

    # Add x265-specific issues to main issues list
    $issues += $x265Issues
    $potentialSavings += $x265Savings
    
    # Rule 3: File size vs duration analysis
    $expectedSizePerHour = switch ($resolutionCategory) {
        "4K UHD" { 12 }  # GB per hour
        "1440p" { 5 }
        "1080p" { 4 }
        "720p" { 2 }
        "SD" { 1 }
    }
    
    $expectedTotalSize = $durationHours * $expectedSizePerHour
    Write-Host "Expected file size: $([math]::Round($expectedTotalSize, 1)) GB (based on $expectedSizePerHour GB/hour for $resolutionCategory)" -ForegroundColor Gray
    
    if ($fileSizeGB -gt $expectedTotalSize * 2) {
        $issues += "⚠️  VERY LARGE file size: $fileSizeGB GB (expected: ~$([math]::Round($expectedTotalSize, 1)) GB)"
        $potentialSavings += $fileSizeGB * 0.4
    }
    elseif ($fileSizeGB -gt $expectedTotalSize * 1.5) {
        $issues += "⚠️  LARGE file size: $fileSizeGB GB (expected: ~$([math]::Round($expectedTotalSize, 1)) GB)"
        $potentialSavings += $fileSizeGB * 0.2
    }
    
    # Rule 4: Codec efficiency
    if ($codec -match "MPEG-2|DivX|XviD" -and $fileSizeGB -gt 2) {
        $issues += "⚠️  OUTDATED CODEC: $codec - could benefit from modern codec (H.264/H.265/AV1)"
        $potentialSavings += $fileSizeGB * 0.6
    }
    
    # Summary
    Write-Host "`n" + "="*50 -ForegroundColor Magenta
    Write-Host "COMPRESSION SUMMARY" -ForegroundColor Magenta
    Write-Host "="*50 -ForegroundColor Magenta
    
    if ($issues.Count -eq 0) {
        Write-Host "✅ COMPRESSION LOOKS GOOD!" -ForegroundColor Green
        Write-Host "This file appears to be reasonably well compressed." -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  COMPRESSION ISSUES DETECTED:" -ForegroundColor Red
        foreach ($issue in $issues) {
            Write-Host "   $issue" -ForegroundColor White
        }
        
        $potentialSavings = [math]::Round($potentialSavings, 2)
        if ($potentialSavings -gt 0) {
            $savingsPercent = [math]::Round(($potentialSavings / $fileSizeGB) * 100, 1)
            Write-Host "`nPotential savings: $potentialSavings GB ($savingsPercent% reduction)" -ForegroundColor Yellow
            
            # Recommendations
            Write-Host "`nRECOMMENDATIONS:" -ForegroundColor Cyan

            if ($x265Issues.Count -gt 0) {
                Write-Host "• OPTIMAL x265 settings for 4K HDR:" -ForegroundColor Yellow
                Write-Host "  crf=18:bframes=6:b-adapt=2:ref=4:rc-lookahead=40:scenecut=40" -ForegroundColor White
                Write-Host "  aq-mode=2:aq-strength=1.0:psy-rd=1.5:psy-rdoq=1.5:me=2:subme=4" -ForegroundColor White
                Write-Host "  rdoq-level=2:ctu=64:deblock=1,1:sao" -ForegroundColor White
                Write-Host "• This could achieve same quality at ~50% smaller file size" -ForegroundColor Green
            }

            if ($codec -notmatch "hevc|h\.265|av1") {
                Write-Host "• Consider re-encoding with H.265 (HEVC) or AV1 for better compression" -ForegroundColor White
            }
            if ($bitrate -gt $expected.optimal * 1.2) {
                Write-Host "• Reduce bitrate to around $($expected.optimal) kbps for optimal quality/size balance" -ForegroundColor White
            }
            Write-Host "• Use CRF (Constant Rate Factor) instead of ABR for better perceptual quality" -ForegroundColor White
            Write-Host "• Use 2-pass encoding if you must use bitrate mode" -ForegroundColor White
        }
    }
    
    Write-Host "`nAnalysis complete!" -ForegroundColor Green
    
} catch {
    Write-Host "Error during analysis: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Gray
    exit 1
}
