# PowerShell script to analyze compression quality of video files in 4K UHD collections
# Identifies poorly compressed files that could be significantly smaller with similar quality

param(
    [string[]]$Paths = @("F:\4K UHD", "N:\4K UHD"),
    [string]$OutputFile = "compression_analysis_results.json"
)

# MediaInfo CLI executable path
$MediaInfoPath = "C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\MediaArea.MediaInfo_Microsoft.Winget.Source_8wekyb3d8bbwe\MediaInfo.exe"

# Video file extensions to analyze
$VideoExtensions = @('.mkv', '.mp4', '.avi', '.m4v', '.mov', '.wmv', '.flv', '.webm', '.ts', '.m2ts')

Write-Host "Batch Video Compression Quality Analysis" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Check if MediaInfo is available
if (-not (Test-Path $MediaInfoPath)) {
    Write-Host "Error: MediaInfo CLI not found at $MediaInfoPath" -ForegroundColor Red
    exit 1
}

# Initialize results
$results = @{
    scanDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    pathsScanned = $Paths
    totalFiles = 0
    analyzedFiles = 0
    poorlyCompressed = @()
    wellCompressed = @()
    likelyRemuxes = @()
    summary = @{
        totalSizeGB = 0
        potentialSavingsGB = 0
        averageBitrate = 0
        codecDistribution = @{}
        x265IssuesFound = @{}
    }
}

# Function to analyze video compression efficiency
function Test-CompressionEfficiency {
    param($FilePath, $MediaInfoJson)
    
    try {
        # Extract key metrics
        $fileSize = (Get-Item $FilePath).Length
        $fileSizeGB = [math]::Round($fileSize / 1GB, 2)
        
        # Find video track in JSON
        $videoTrack = $MediaInfoJson.media.track | Where-Object { $_.'@type' -eq 'Video' } | Select-Object -First 1
        
        if (-not $videoTrack) {
            Write-Warning "No video track found in $FilePath"
            return $null
        }
        
        # Extract video properties
        $codec = if ($videoTrack.Format) { $videoTrack.Format } else { "Unknown" }
        $profile = if ($videoTrack.Format_Profile) { $videoTrack.Format_Profile } else { "Unknown" }
        $bitrate = if ($videoTrack.BitRate) { [int]($videoTrack.BitRate / 1000) } else { 0 }  # Convert to kbps
        $width = if ($videoTrack.Width) { [int]$videoTrack.Width } else { 0 }
        $height = if ($videoTrack.Height) { [int]$videoTrack.Height } else { 0 }
        $frameRate = if ($videoTrack.FrameRate) { [float]$videoTrack.FrameRate } else { 0 }
        $duration = if ($videoTrack.Duration) { [float]$videoTrack.Duration } else { 0 }  # in seconds
        $bitDepth = if ($videoTrack.BitDepth) { [int]$videoTrack.BitDepth } else { 8 }
        
        # Calculate derived metrics
        $resolution = "${width}x${height}"
        $durationMinutes = [math]::Round($duration / 60, 1)
        $durationHours = [math]::Round($duration / 3600, 2)
        $pixelsPerFrame = $width * $height
        $totalFrames = if ($frameRate -gt 0) { $duration * $frameRate } else { 0 }
        $totalPixels = $pixelsPerFrame * $totalFrames
        $bitsPerPixel = if ($totalPixels -gt 0) { ($fileSize * 8) / $totalPixels } else { 0 }
        
        # Determine resolution category
        $resolutionCategory = if ($width -ge 3840) { 
            "4K UHD" 
        } elseif ($width -ge 2560) { 
            "1440p" 
        } elseif ($width -ge 1920) { 
            "1080p" 
        } elseif ($width -ge 1280) { 
            "720p" 
        } else { 
            "SD" 
        }
        
        # Expected bitrate ranges (conservative estimates)
        $expectedBitrates = @{
            "4K UHD" = @{ min = 15000; max = 25000; optimal = 20000 }
            "1440p" = @{ min = 6000; max = 12000; optimal = 8000 }
            "1080p" = @{ min = 3000; max = 8000; optimal = 5000 }
            "720p" = @{ min = 1500; max = 4000; optimal = 2500 }
            "SD" = @{ min = 500; max = 2000; optimal = 1000 }
        }
        
        $expected = $expectedBitrates[$resolutionCategory]
        
        # Initialize analysis
        $issues = @()
        $potentialSavings = 0
        $x265Issues = @()
        $x265Savings = 0
        
        # Advanced x265/HEVC compression efficiency analysis
        $encodingLibrary = if ($videoTrack.Encoded_Library) { $videoTrack.Encoded_Library } else { "Unknown" }
        $encodingSettings = if ($videoTrack.Encoded_Library_Settings) { $videoTrack.Encoded_Library_Settings } else { "" }
        
        # Analyze x265 settings if available
        if ($encodingLibrary -match "x265" -and $encodingSettings.Length -gt 0) {
            # Parse x265 settings (handle both : and / separators)
            $settings = @{}
            $settingsParts = $encodingSettings -split '[:/]' | Where-Object { $_.Trim().Length -gt 0 }
            
            foreach ($part in $settingsParts) {
                $part = $part.Trim()
                if ($part -match '^([^=\s]+)=(.+)$') {
                    $key = $matches[1].Trim()
                    $value = $matches[2].Trim()
                    $settings[$key] = $value
                } elseif ($part -match '^(no-.+)$' -or $part -match '^([a-zA-Z-]+)$') {
                    $key = $matches[1].Trim()
                    $settings[$key] = $true
                }
            }
            
            # Check for inefficient settings based on analysis
            
            # 1. Reference frames
            if ($settings.ContainsKey('ref') -and [int]$settings['ref'] -lt 3) {
                $x265Issues += "LOW reference frames: ref=$($settings['ref']) (efficient: ref=4)"
                $x265Savings += $fileSizeGB * 0.15
            }
            
            # 2. Motion estimation
            if ($settings.ContainsKey('me') -and [int]$settings['me'] -eq 0) {
                $x265Issues += "NO motion estimation: me=0 (efficient: me=2-3)"
                $x265Savings += $fileSizeGB * 0.25
            }
            if ($settings.ContainsKey('subme') -and [int]$settings['subme'] -eq 0) {
                $x265Issues += "NO subpixel motion estimation: subme=0 (efficient: subme=3-5)"
                $x265Savings += $fileSizeGB * 0.20
            }
            
            # 3. B-frames
            if ($settings.ContainsKey('b-adapt') -and [int]$settings['b-adapt'] -eq 0) {
                $x265Issues += "NO adaptive B-frames: b-adapt=0 (efficient: b-adapt=2)"
                $x265Savings += $fileSizeGB * 0.10
            }
            
            # 4. Rate control lookahead
            if ($settings.ContainsKey('rc-lookahead') -and [int]$settings['rc-lookahead'] -lt 20) {
                $x265Issues += "SHORT lookahead: rc-lookahead=$($settings['rc-lookahead']) (efficient: 40-60)"
                $x265Savings += $fileSizeGB * 0.08
            }
            
            # 5. Scene cut detection
            if ($settings.ContainsKey('scenecut') -and [int]$settings['scenecut'] -eq 0) {
                $x265Issues += "SCENE CUT disabled: scenecut=0 (efficient: scenecut=40)"
                $x265Savings += $fileSizeGB * 0.12
            }
            
            # 6. Adaptive quantization
            if ($settings.ContainsKey('aq-strength') -and [float]$settings['aq-strength'] -eq 0.0) {
                $x265Issues += "NO adaptive quantization: aq-strength=0.00 (efficient: aq-strength=0.8-1.2)"
                $x265Savings += $fileSizeGB * 0.15
            }
            
            # 7. SAO filter
            if ($settings.ContainsKey('no-sao')) {
                $x265Issues += "SAO DISABLED: no-sao (efficient: enable sao)"
                $x265Savings += $fileSizeGB * 0.05
            }
            
            # 8. Rate control mode
            if ($settings.ContainsKey('rc') -and $settings['rc'] -eq 'abr') {
                $x265Issues += "ABR rate control: less efficient than CRF (efficient: crf=17-22 for 4K HDR)"
                $x265Savings += $fileSizeGB * 0.20
            }
            
            # 9. CTU size
            if ($settings.ContainsKey('ctu') -and [int]$settings['ctu'] -lt 64 -and $width -ge 3840) {
                $x265Issues += "SMALL CTU size: ctu=$($settings['ctu']) (efficient for 4K: ctu=64)"
                $x265Savings += $fileSizeGB * 0.05
            }
            
            # 10. Psychovisual settings
            if ($settings.ContainsKey('psy-rdoq') -and [float]$settings['psy-rdoq'] -gt 3.0) {
                $x265Issues += "EXCESSIVE psy-rdoq: psy-rdoq=$($settings['psy-rdoq']) (efficient: 1.0-2.0)"
                $x265Savings += $fileSizeGB * 0.08
            }
        }
        
        # Standard compression analysis
        
        # Rule 1: Bitrate analysis
        if ($bitrate -gt $expected.max * 1.5) {
            $issues += "EXCESSIVE BITRATE: $bitrate kbps (expected max: $($expected.max) kbps)"
            $potentialSavings += $fileSizeGB * 0.3
        }
        elseif ($bitrate -gt $expected.max) {
            $issues += "HIGH BITRATE: $bitrate kbps (recommended max: $($expected.max) kbps)"
            $potentialSavings += $fileSizeGB * 0.15
        }
        
        # Rule 2: Bits per pixel analysis
        $maxBitsPerPixel = switch ($codec.ToLower()) {
            { $_ -match "hevc|h\.265" } { 0.15 }
            { $_ -match "avc|h\.264" } { 0.25 }
            { $_ -match "av1" } { 0.12 }
            { $_ -match "vp9" } { 0.18 }
            default { 0.3 }
        }
        
        if ($bitsPerPixel -gt $maxBitsPerPixel * 1.5) {
            $issues += "VERY HIGH bits per pixel: $([math]::Round($bitsPerPixel, 4)) (efficient max: $maxBitsPerPixel)"
            $potentialSavings += $fileSizeGB * 0.25
        }
        elseif ($bitsPerPixel -gt $maxBitsPerPixel) {
            $issues += "HIGH bits per pixel: $([math]::Round($bitsPerPixel, 4)) (recommended max: $maxBitsPerPixel)"
            $potentialSavings += $fileSizeGB * 0.1
        }
        
        # Rule 3: File size vs duration analysis
        $expectedSizePerHour = switch ($resolutionCategory) {
            "4K UHD" { 12 }  # GB per hour
            "1440p" { 5 }
            "1080p" { 4 }
            "720p" { 2 }
            "SD" { 1 }
        }
        
        $expectedTotalSize = $durationHours * $expectedSizePerHour
        
        if ($fileSizeGB -gt $expectedTotalSize * 2) {
            $issues += "VERY LARGE file size: $fileSizeGB GB (expected: ~$([math]::Round($expectedTotalSize, 1)) GB)"
            $potentialSavings += $fileSizeGB * 0.4
        }
        elseif ($fileSizeGB -gt $expectedTotalSize * 1.5) {
            $issues += "LARGE file size: $fileSizeGB GB (expected: ~$([math]::Round($expectedTotalSize, 1)) GB)"
            $potentialSavings += $fileSizeGB * 0.2
        }
        
        # Rule 4: Codec efficiency
        if ($codec -match "MPEG-2|DivX|XviD" -and $fileSizeGB -gt 2) {
            $issues += "OUTDATED CODEC: $codec - could benefit from modern codec (H.264/H.265/AV1)"
            $potentialSavings += $fileSizeGB * 0.6
        }
        
        # Add x265-specific issues to main issues list
        $issues += $x265Issues
        $potentialSavings += $x265Savings

        # Determine if poorly compressed
        # Exclude files >45GB as they are likely Blu-ray remuxes (not compressed files)
        $isLikelyRemux = $fileSizeGB -gt 45
        $isPoorlyCompressed = -not $isLikelyRemux -and (($issues.Count -gt 0) -or ($x265Issues.Count -gt 0))
        
        return @{
            isPoorlyCompressed = $isPoorlyCompressed
            issues = $issues
            x265Issues = $x265Issues
            potentialSavingsGB = [math]::Round($potentialSavings, 2)
            metrics = @{
                codec = $codec
                profile = $profile
                encodingLibrary = $encodingLibrary
                bitrate = $bitrate
                resolution = $resolution
                resolutionCategory = $resolutionCategory
                frameRate = $frameRate
                bitDepth = $bitDepth
                durationMinutes = $durationMinutes
                durationHours = $durationHours
                fileSizeGB = $fileSizeGB
                bitsPerPixel = [math]::Round($bitsPerPixel, 4)
            }
        }
    }
    catch {
        Write-Warning "Error analyzing $FilePath`: $($_.Exception.Message)"
        return $null
    }
}

# Scan each path
foreach ($path in $Paths) {
    if (-not (Test-Path $path)) {
        Write-Warning "Path not found: $path"
        continue
    }
    
    Write-Host "`nScanning: $path" -ForegroundColor Cyan
    
    # Get all video files recursively
    $videoFiles = Get-ChildItem -Path $path -Recurse -File | Where-Object { 
        $VideoExtensions -contains $_.Extension.ToLower() 
    }
    
    $results.totalFiles += $videoFiles.Count
    Write-Host "Found $($videoFiles.Count) video files" -ForegroundColor Yellow
    
    $fileCount = 0
    foreach ($file in $videoFiles) {
        $fileCount++
        Write-Host "[$fileCount/$($videoFiles.Count)] Analyzing: $($file.Name)" -ForegroundColor Gray

        try {
            # Run MediaInfo with JSON output
            $mediaInfoJson = & $MediaInfoPath --Output=JSON $file.FullName | ConvertFrom-Json

            if ($mediaInfoJson -and $mediaInfoJson.media) {
                $results.analyzedFiles++

                # Analyze compression efficiency
                $analysis = Test-CompressionEfficiency -FilePath $file.FullName -MediaInfoJson $mediaInfoJson

                if ($analysis) {
                    # Update summary statistics
                    $results.summary.totalSizeGB += $analysis.metrics.fileSizeGB
                    $results.summary.potentialSavingsGB += $analysis.potentialSavingsGB

                    # Track codec distribution
                    $codec = $analysis.metrics.codec
                    if ($results.summary.codecDistribution.ContainsKey($codec)) {
                        $results.summary.codecDistribution[$codec]++
                    } else {
                        $results.summary.codecDistribution[$codec] = 1
                    }

                    # Track x265 issues
                    foreach ($issue in $analysis.x265Issues) {
                        $issueType = $issue.Split(':')[0]
                        if ($results.summary.x265IssuesFound.ContainsKey($issueType)) {
                            $results.summary.x265IssuesFound[$issueType]++
                        } else {
                            $results.summary.x265IssuesFound[$issueType] = 1
                        }
                    }

                    if ($analysis.metrics.fileSizeGB -gt 45) {
                        # Likely Blu-ray remux - don't analyze for compression efficiency
                        $results.likelyRemuxes += @{
                            fileName = $file.Name
                            fullPath = $file.FullName
                            directory = $file.Directory.FullName
                            metrics = $analysis.metrics
                        }

                        Write-Host "  📀 LIKELY REMUX (>45GB) - Skipping compression analysis" -ForegroundColor Cyan
                    } elseif ($analysis.isPoorlyCompressed) {
                        $results.poorlyCompressed += @{
                            fileName = $file.Name
                            fullPath = $file.FullName
                            directory = $file.Directory.FullName
                            issues = $analysis.issues
                            x265Issues = $analysis.x265Issues
                            potentialSavingsGB = $analysis.potentialSavingsGB
                            metrics = $analysis.metrics
                        }

                        Write-Host "  ⚠️  POORLY COMPRESSED! Potential savings: $($analysis.potentialSavingsGB) GB" -ForegroundColor Red
                    } else {
                        $results.wellCompressed += @{
                            fileName = $file.Name
                            fullPath = $file.FullName
                            metrics = $analysis.metrics
                        }

                        Write-Host "  ✅ Well compressed" -ForegroundColor Green
                    }

                    # Save partial results every 10 files
                    if ($fileCount % 10 -eq 0) {
                        $partialResults = $results.Clone()
                        $partialResults.summary.totalSizeGB = [math]::Round($partialResults.summary.totalSizeGB, 2)
                        $partialResults.summary.potentialSavingsGB = [math]::Round($partialResults.summary.potentialSavingsGB, 2)
                        $partialResults.summary.averageBitrate = if ($partialResults.analyzedFiles -gt 0) {
                            [math]::Round((($partialResults.poorlyCompressed + $partialResults.wellCompressed | ForEach-Object { $_.metrics.bitrate }) | Measure-Object -Average).Average, 0)
                        } else { 0 }
                        $partialResults | ConvertTo-Json -Depth 10 | Out-File -FilePath "partial_$OutputFile" -Encoding UTF8
                        Write-Host "    💾 Partial results saved (analyzed $($results.analyzedFiles) files)" -ForegroundColor Cyan
                    }
                }
            }
        }
        catch {
            Write-Warning "Failed to analyze $($file.FullName): $($_.Exception.Message)"
        }
    }

    # Save results after completing each directory
    $partialResults = $results.Clone()
    $partialResults.summary.totalSizeGB = [math]::Round($partialResults.summary.totalSizeGB, 2)
    $partialResults.summary.potentialSavingsGB = [math]::Round($partialResults.summary.potentialSavingsGB, 2)
    $partialResults.summary.averageBitrate = if ($partialResults.analyzedFiles -gt 0) {
        [math]::Round((($partialResults.poorlyCompressed + $partialResults.wellCompressed | ForEach-Object { $_.metrics.bitrate }) | Measure-Object -Average).Average, 0)
    } else { 0 }
    $partialResults | ConvertTo-Json -Depth 10 | Out-File -FilePath "partial_$OutputFile" -Encoding UTF8
    Write-Host "💾 Directory completed - partial results saved" -ForegroundColor Green
}

# Finalize summary
$results.summary.totalSizeGB = [math]::Round($results.summary.totalSizeGB, 2)
$results.summary.potentialSavingsGB = [math]::Round($results.summary.potentialSavingsGB, 2)
$results.summary.averageBitrate = if ($results.analyzedFiles -gt 0) { 
    [math]::Round((($results.poorlyCompressed + $results.wellCompressed | ForEach-Object { $_.metrics.bitrate }) | Measure-Object -Average).Average, 0)
} else { 0 }

# Export results
$results | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputFile -Encoding UTF8

# Display summary
Write-Host "`n" + "="*70 -ForegroundColor Green
Write-Host "BATCH COMPRESSION ANALYSIS COMPLETE" -ForegroundColor Green
Write-Host "="*70 -ForegroundColor Green
Write-Host "Total files found: $($results.totalFiles)" -ForegroundColor White
Write-Host "Files analyzed: $($results.analyzedFiles)" -ForegroundColor White
Write-Host "Poorly compressed files: $($results.poorlyCompressed.Count)" -ForegroundColor Red
Write-Host "Well compressed files: $($results.wellCompressed.Count)" -ForegroundColor Green
Write-Host "Likely remuxes (>45GB): $($results.likelyRemuxes.Count)" -ForegroundColor Cyan
Write-Host "Total collection size: $($results.summary.totalSizeGB) GB" -ForegroundColor Cyan
Write-Host "Potential savings: $($results.summary.potentialSavingsGB) GB" -ForegroundColor Yellow

$savingsPercentage = if ($results.summary.totalSizeGB -gt 0) { 
    [math]::Round(($results.summary.potentialSavingsGB / $results.summary.totalSizeGB) * 100, 1) 
} else { 0 }
Write-Host "Potential space savings: $savingsPercentage%" -ForegroundColor Yellow

if ($results.poorlyCompressed.Count -gt 0) {
    Write-Host "`nTop 10 poorly compressed files by potential savings:" -ForegroundColor Red
    $results.poorlyCompressed | Sort-Object potentialSavingsGB -Descending | Select-Object -First 10 | ForEach-Object {
        Write-Host "  • $($_.fileName) - $($_.potentialSavingsGB) GB savings" -ForegroundColor White
        Write-Host "    Resolution: $($_.metrics.resolution) | Codec: $($_.metrics.codec) | Size: $($_.metrics.fileSizeGB) GB" -ForegroundColor Gray
    }
    
    Write-Host "`nMost common x265 compression issues:" -ForegroundColor Yellow
    $results.summary.x265IssuesFound.GetEnumerator() | Sort-Object Value -Descending | ForEach-Object {
        Write-Host "  • $($_.Key): $($_.Value) files" -ForegroundColor White
    }
}

Write-Host "`nDetailed results saved to: $OutputFile" -ForegroundColor Green
Write-Host "Analysis complete!" -ForegroundColor Green
