# Script to analyze movies without SRT subtitles
# Reads the Plex metadata JSON file and counts movies that don't have at least one SRT subtitle

param(
    [string]$JsonFile = "Library - MOVIES-RAUDEL - All Metadata.json"
)

Write-Host "Analyzing movies for SRT subtitles..." -ForegroundColor Green
Write-Host "Reading file: $JsonFile" -ForegroundColor Yellow

# Read and parse the JSON file
try {
    $jsonContent = Get-Content $JsonFile -Raw | ConvertFrom-Json
    Write-Host "Successfully loaded $($jsonContent.Count) movies from the metadata file." -ForegroundColor Green
}
catch {
    Write-Error "Failed to read or parse JSON file: $_"
    exit 1
}

# Initialize counters
$totalMovies = 0
$moviesWithSRT = 0
$moviesWithoutSRT = 0
$moviesWithoutSRTList = @()

# Analyze each movie
foreach ($movie in $jsonContent) {
    $totalMovies++
    $movieTitle = $movie.title
    $movieYear = $movie.year

    # Check if movie has media files
    if ($movie.media -and $movie.media.Count -gt 0) {
        $hasSRT = $false

        # Check each media file for SRT subtitles
        foreach ($mediaFile in $movie.media) {
            # Check each part within the media file
            if ($mediaFile.parts -and $mediaFile.parts.Count -gt 0) {
                foreach ($part in $mediaFile.parts) {
                    if ($part.subtitleStreams -and $part.subtitleStreams.Count -gt 0) {
                        # Check if any subtitle stream has SRT codec
                        foreach ($subtitleStream in $part.subtitleStreams) {
                            if ($subtitleStream.codec -eq "srt") {
                                $hasSRT = $true
                                break
                            }
                        }
                        if ($hasSRT) { break }
                    }
                }
                if ($hasSRT) { break }
            }
        }

        if ($hasSRT) {
            $moviesWithSRT++
        } else {
            $moviesWithoutSRT++
            $moviesWithoutSRTList += [PSCustomObject]@{
                Title = $movieTitle
                Year = $movieYear
                Key = $movie.key
            }
        }
    } else {
        # Movie has no media files, count as without SRT
        $moviesWithoutSRT++
        $moviesWithoutSRTList += [PSCustomObject]@{
            Title = $movieTitle
            Year = $movieYear
            Key = $movie.key
            Note = "No media files"
        }
    }

    # Progress indicator
    if ($totalMovies % 100 -eq 0) {
        Write-Host "Processed $totalMovies movies..." -ForegroundColor Cyan
    }
}

# Display results
Write-Host "`n" + "="*60 -ForegroundColor Magenta
Write-Host "SUBTITLE ANALYSIS RESULTS" -ForegroundColor Magenta
Write-Host "="*60 -ForegroundColor Magenta

Write-Host "Total movies analyzed: $totalMovies" -ForegroundColor White
Write-Host "Movies WITH SRT subtitles: $moviesWithSRT" -ForegroundColor Green
Write-Host "Movies WITHOUT SRT subtitles: $moviesWithoutSRT" -ForegroundColor Red

$percentageWithSRT = [math]::Round(($moviesWithSRT / $totalMovies) * 100, 2)
$percentageWithoutSRT = [math]::Round(($moviesWithoutSRT / $totalMovies) * 100, 2)

Write-Host "Percentage with SRT: $percentageWithSRT%" -ForegroundColor Green
Write-Host "Percentage without SRT: $percentageWithoutSRT%" -ForegroundColor Red

# Show movies without SRT subtitles
if ($moviesWithoutSRT -gt 0) {
    Write-Host "`nMovies WITHOUT SRT subtitles:" -ForegroundColor Yellow
    Write-Host "-" * 40 -ForegroundColor Yellow
    
    $moviesWithoutSRTList | Sort-Object Title | ForEach-Object {
        $displayText = "$($_.Title) ($($_.Year))"
        if ($_.Note) {
            $displayText += " - $($_.Note)"
        }
        Write-Host $displayText -ForegroundColor White
    }
}

Write-Host "`nAnalysis complete!" -ForegroundColor Green
