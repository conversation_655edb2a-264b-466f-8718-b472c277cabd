# PowerShell script to analyze compression quality from cached metadata
# Fast analysis without re-running MediaInfo

param(
    [string]$CacheFile = "video_metadata_cache.json",
    [string]$OutputFile = "compression_analysis_from_cache.json"
)

Write-Host "Fast Compression Analysis (from cached metadata)" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Check if cache file exists
if (-not (Test-Path $CacheFile)) {
    Write-Host "Error: Cache file not found: $CacheFile" -ForegroundColor Red
    Write-Host "Run extract_metadata.ps1 first to create the cache file." -ForegroundColor Yellow
    exit 1
}

# Load cached metadata
Write-Host "Loading cached metadata from: $CacheFile" -ForegroundColor Cyan
try {
    $metadataCache = Get-Content $CacheFile -Raw | ConvertFrom-Json
    Write-Host "Loaded metadata for $($metadataCache.videoFiles.Count) files" -ForegroundColor Green
} catch {
    Write-Host "Error loading cache file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Initialize results
$results = @{
    analysisDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    cacheFile = $CacheFile
    cacheDate = $metadataCache.extractionDate
    totalFiles = $metadataCache.videoFiles.Count
    analyzedFiles = 0
    poorlyCompressed = @()
    wellCompressed = @()
    likelyRemuxes = @()
    summary = @{
        totalSizeGB = 0
        potentialSavingsGB = 0
        averageBitrate = 0
        codecDistribution = @{}
        x265IssuesFound = @{}
    }
}

Write-Host "Analyzing compression efficiency..." -ForegroundColor Yellow

# Function to analyze compression efficiency from cached metadata
function Test-CompressionFromCache {
    param($Metadata)
    
    try {
        # Skip remuxes (>40GB)
        if ($Metadata.isLikelyRemux) {
            return @{
                isLikelyRemux = $true
                isPoorlyCompressed = $false
                issues = @()
                x265Issues = @()
                potentialSavingsGB = 0
            }
        }
        
        # Initialize analysis
        $issues = @()
        $potentialSavings = 0
        $x265Issues = @()
        $x265Savings = 0
        
        # Expected bitrate ranges (conservative estimates)
        $expectedBitrates = @{
            "4K UHD" = @{ min = 15000; max = 25000; optimal = 20000 }
            "1440p" = @{ min = 6000; max = 12000; optimal = 8000 }
            "1080p" = @{ min = 3000; max = 8000; optimal = 5000 }
            "720p" = @{ min = 1500; max = 4000; optimal = 2500 }
            "SD" = @{ min = 500; max = 2000; optimal = 1000 }
        }
        
        $expected = $expectedBitrates[$Metadata.resolutionCategory]
        
        # Analyze x265 settings if available
        if ($Metadata.encodedLibrary -match "x265" -and $Metadata.encodedLibrarySettings.Length -gt 0) {
            # Parse x265 settings (handle both : and / separators)
            $settings = @{}
            $settingsParts = $Metadata.encodedLibrarySettings -split '[:/]' | Where-Object { $_.Trim().Length -gt 0 }
            
            foreach ($part in $settingsParts) {
                $part = $part.Trim()
                if ($part -match '^([^=\s]+)=(.+)$') {
                    $key = $matches[1].Trim()
                    $value = $matches[2].Trim()
                    $settings[$key] = $value
                } elseif ($part -match '^(no-.+)$' -or $part -match '^([a-zA-Z-]+)$') {
                    $key = $matches[1].Trim()
                    $settings[$key] = $true
                }
            }
            
            # Check for inefficient settings based on analysis
            
            # 1. Reference frames
            if ($settings.ContainsKey('ref') -and [int]$settings['ref'] -lt 3) {
                $x265Issues += "LOW reference frames: ref=$($settings['ref']) (efficient: ref=4)"
                $x265Savings += $Metadata.fileSizeGB * 0.15
            }
            
            # 2. Motion estimation
            if ($settings.ContainsKey('me') -and [int]$settings['me'] -eq 0) {
                $x265Issues += "NO motion estimation: me=0 (efficient: me=2-3)"
                $x265Savings += $Metadata.fileSizeGB * 0.25
            }
            if ($settings.ContainsKey('subme') -and [int]$settings['subme'] -eq 0) {
                $x265Issues += "NO subpixel motion estimation: subme=0 (efficient: subme=3-5)"
                $x265Savings += $Metadata.fileSizeGB * 0.20
            }
            
            # 3. B-frames
            if ($settings.ContainsKey('b-adapt') -and [int]$settings['b-adapt'] -eq 0) {
                $x265Issues += "NO adaptive B-frames: b-adapt=0 (efficient: b-adapt=2)"
                $x265Savings += $Metadata.fileSizeGB * 0.10
            }
            
            # 4. Rate control lookahead
            if ($settings.ContainsKey('rc-lookahead') -and [int]$settings['rc-lookahead'] -lt 20) {
                $x265Issues += "SHORT lookahead: rc-lookahead=$($settings['rc-lookahead']) (efficient: 40-60)"
                $x265Savings += $Metadata.fileSizeGB * 0.08
            }
            
            # 5. Scene cut detection
            if ($settings.ContainsKey('scenecut') -and [int]$settings['scenecut'] -eq 0) {
                $x265Issues += "SCENE CUT disabled: scenecut=0 (efficient: scenecut=40)"
                $x265Savings += $Metadata.fileSizeGB * 0.12
            }
            
            # 6. Adaptive quantization
            if ($settings.ContainsKey('aq-strength') -and [float]$settings['aq-strength'] -eq 0.0) {
                $x265Issues += "NO adaptive quantization: aq-strength=0.00 (efficient: aq-strength=0.8-1.2)"
                $x265Savings += $Metadata.fileSizeGB * 0.15
            }
            
            # 7. SAO filter
            if ($settings.ContainsKey('no-sao')) {
                $x265Issues += "SAO DISABLED: no-sao (efficient: enable sao)"
                $x265Savings += $Metadata.fileSizeGB * 0.05
            }
            
            # 8. Rate control mode
            if ($settings.ContainsKey('rc') -and $settings['rc'] -eq 'abr') {
                $x265Issues += "ABR rate control: less efficient than CRF (efficient: crf=17-22 for 4K HDR)"
                $x265Savings += $Metadata.fileSizeGB * 0.20
            }
            
            # 9. CTU size
            if ($settings.ContainsKey('ctu') -and [int]$settings['ctu'] -lt 64 -and $Metadata.width -ge 3840) {
                $x265Issues += "SMALL CTU size: ctu=$($settings['ctu']) (efficient for 4K: ctu=64)"
                $x265Savings += $Metadata.fileSizeGB * 0.05
            }
            
            # 10. Psychovisual settings
            if ($settings.ContainsKey('psy-rdoq') -and [float]$settings['psy-rdoq'] -gt 3.0) {
                $x265Issues += "EXCESSIVE psy-rdoq: psy-rdoq=$($settings['psy-rdoq']) (efficient: 1.0-2.0)"
                $x265Savings += $Metadata.fileSizeGB * 0.08
            }
        }
        
        # Standard compression analysis
        
        # Rule 1: Bitrate analysis
        if ($Metadata.bitRateKbps -gt $expected.max * 1.5) {
            $issues += "EXCESSIVE BITRATE: $($Metadata.bitRateKbps) kbps (expected max: $($expected.max) kbps)"
            $potentialSavings += $Metadata.fileSizeGB * 0.3
        }
        elseif ($Metadata.bitRateKbps -gt $expected.max) {
            $issues += "HIGH BITRATE: $($Metadata.bitRateKbps) kbps (recommended max: $($expected.max) kbps)"
            $potentialSavings += $Metadata.fileSizeGB * 0.15
        }
        
        # Rule 2: Bits per pixel analysis
        $maxBitsPerPixel = switch ($Metadata.format.ToLower()) {
            { $_ -match "hevc|h\.265" } { 0.15 }
            { $_ -match "avc|h\.264" } { 0.25 }
            { $_ -match "av1" } { 0.12 }
            { $_ -match "vp9" } { 0.18 }
            default { 0.3 }
        }
        
        if ($Metadata.bitsPerPixel -gt $maxBitsPerPixel * 1.5) {
            $issues += "VERY HIGH bits per pixel: $([math]::Round($Metadata.bitsPerPixel, 4)) (efficient max: $maxBitsPerPixel)"
            $potentialSavings += $Metadata.fileSizeGB * 0.25
        }
        elseif ($Metadata.bitsPerPixel -gt $maxBitsPerPixel) {
            $issues += "HIGH bits per pixel: $([math]::Round($Metadata.bitsPerPixel, 4)) (recommended max: $maxBitsPerPixel)"
            $potentialSavings += $Metadata.fileSizeGB * 0.1
        }
        
        # Rule 3: File size vs duration analysis
        $expectedSizePerHour = switch ($Metadata.resolutionCategory) {
            "4K UHD" { 12 }  # GB per hour
            "1440p" { 5 }
            "1080p" { 4 }
            "720p" { 2 }
            "SD" { 1 }
        }
        
        $expectedTotalSize = $Metadata.durationHours * $expectedSizePerHour
        
        if ($Metadata.fileSizeGB -gt $expectedTotalSize * 2) {
            $issues += "VERY LARGE file size: $($Metadata.fileSizeGB) GB (expected: ~$([math]::Round($expectedTotalSize, 1)) GB)"
            $potentialSavings += $Metadata.fileSizeGB * 0.4
        }
        elseif ($Metadata.fileSizeGB -gt $expectedTotalSize * 1.5) {
            $issues += "LARGE file size: $($Metadata.fileSizeGB) GB (expected: ~$([math]::Round($expectedTotalSize, 1)) GB)"
            $potentialSavings += $Metadata.fileSizeGB * 0.2
        }
        
        # Rule 4: Codec efficiency
        if ($Metadata.format -match "MPEG-2|DivX|XviD" -and $Metadata.fileSizeGB -gt 2) {
            $issues += "OUTDATED CODEC: $($Metadata.format) - could benefit from modern codec (H.264/H.265/AV1)"
            $potentialSavings += $Metadata.fileSizeGB * 0.6
        }
        
        # Add x265-specific issues to main issues list
        $issues += $x265Issues
        $potentialSavings += $x265Savings
        
        # Determine if poorly compressed
        $isPoorlyCompressed = ($issues.Count -gt 0) -or ($x265Issues.Count -gt 0)
        
        return @{
            isLikelyRemux = $false
            isPoorlyCompressed = $isPoorlyCompressed
            issues = $issues
            x265Issues = $x265Issues
            potentialSavingsGB = [math]::Round($potentialSavings, 2)
        }
        
    } catch {
        Write-Warning "Error analyzing $($Metadata.fileName): $($_.Exception.Message)"
        return $null
    }
}

# Analyze each file from cache
$fileCount = 0
foreach ($metadata in $metadataCache.videoFiles) {
    $fileCount++
    Write-Host "[$fileCount/$($metadataCache.videoFiles.Count)] Analyzing: $($metadata.fileName)" -ForegroundColor Gray
    
    $results.analyzedFiles++
    
    # Analyze compression efficiency
    $analysis = Test-CompressionFromCache -Metadata $metadata
    
    if ($analysis) {
        # Update summary statistics (only for non-remux files)
        if (-not $analysis.isLikelyRemux) {
            $results.summary.totalSizeGB += $metadata.fileSizeGB
            $results.summary.potentialSavingsGB += $analysis.potentialSavingsGB
        }
        
        # Track codec distribution
        $codec = $metadata.format
        if ($results.summary.codecDistribution.ContainsKey($codec)) {
            $results.summary.codecDistribution[$codec]++
        } else {
            $results.summary.codecDistribution[$codec] = 1
        }
        
        # Track x265 issues (only for non-remux files)
        if (-not $analysis.isLikelyRemux) {
            foreach ($issue in $analysis.x265Issues) {
                $issueType = $issue.Split(':')[0]
                if ($results.summary.x265IssuesFound.ContainsKey($issueType)) {
                    $results.summary.x265IssuesFound[$issueType]++
                } else {
                    $results.summary.x265IssuesFound[$issueType] = 1
                }
            }
        }
        
        # Categorize the file
        if ($analysis.isLikelyRemux) {
            $results.likelyRemuxes += @{
                fileName = $metadata.fileName
                fullPath = $metadata.fullPath
                directory = $metadata.directory
                metrics = $metadata
            }
            
            Write-Host "  [REMUX] Likely Blu-ray remux (>40GB) - $($metadata.fileSizeGB) GB" -ForegroundColor Cyan
        } elseif ($analysis.isPoorlyCompressed) {
            $results.poorlyCompressed += @{
                fileName = $metadata.fileName
                fullPath = $metadata.fullPath
                directory = $metadata.directory
                issues = $analysis.issues
                x265Issues = $analysis.x265Issues
                potentialSavingsGB = $analysis.potentialSavingsGB
                metrics = $metadata
            }
            
            Write-Host "  [POOR] Poorly compressed! Potential savings: $($analysis.potentialSavingsGB) GB" -ForegroundColor Red
        } else {
            $results.wellCompressed += @{
                fileName = $metadata.fileName
                fullPath = $metadata.fullPath
                metrics = $metadata
            }
            
            Write-Host "  [GOOD] Well compressed" -ForegroundColor Green
        }
    }
}

# Finalize summary
$results.summary.totalSizeGB = [math]::Round($results.summary.totalSizeGB, 2)
$results.summary.potentialSavingsGB = [math]::Round($results.summary.potentialSavingsGB, 2)
$results.summary.averageBitrate = if ($results.analyzedFiles -gt 0) { 
    [math]::Round((($results.poorlyCompressed + $results.wellCompressed | ForEach-Object { $_.metrics.bitRateKbps }) | Measure-Object -Average).Average, 0)
} else { 0 }

# Export results
$results | ConvertTo-Json -Depth 10 | Out-File -FilePath $OutputFile -Encoding UTF8

# Display summary
Write-Host "`n" + "="*70 -ForegroundColor Green
Write-Host "FAST COMPRESSION ANALYSIS COMPLETE" -ForegroundColor Green
Write-Host "="*70 -ForegroundColor Green
Write-Host "Total files analyzed: $($results.analyzedFiles)" -ForegroundColor White
Write-Host "Poorly compressed files: $($results.poorlyCompressed.Count)" -ForegroundColor Red
Write-Host "Well compressed files: $($results.wellCompressed.Count)" -ForegroundColor Green
Write-Host "Likely remuxes (>40GB): $($results.likelyRemuxes.Count)" -ForegroundColor Cyan
Write-Host "Total compressed collection size: $($results.summary.totalSizeGB) GB" -ForegroundColor Cyan
Write-Host "Potential savings: $($results.summary.potentialSavingsGB) GB" -ForegroundColor Yellow

$savingsPercentage = if ($results.summary.totalSizeGB -gt 0) { 
    [math]::Round(($results.summary.potentialSavingsGB / $results.summary.totalSizeGB) * 100, 1) 
} else { 0 }
Write-Host "Potential space savings: $savingsPercentage%" -ForegroundColor Yellow

if ($results.poorlyCompressed.Count -gt 0) {
    Write-Host "`nTop 10 poorly compressed files by potential savings:" -ForegroundColor Red
    $results.poorlyCompressed | Sort-Object potentialSavingsGB -Descending | Select-Object -First 10 | ForEach-Object {
        Write-Host "  • $($_.fileName) - $($_.potentialSavingsGB) GB savings" -ForegroundColor White
        Write-Host "    Resolution: $($_.metrics.resolution) | Codec: $($_.metrics.format) | Size: $($_.metrics.fileSizeGB) GB" -ForegroundColor Gray
    }
    
    Write-Host "`nMost common x265 compression issues:" -ForegroundColor Yellow
    $results.summary.x265IssuesFound.GetEnumerator() | Sort-Object Value -Descending | ForEach-Object {
        Write-Host "  • $($_.Key): $($_.Value) files" -ForegroundColor White
    }
}

Write-Host "`nDetailed results saved to: $OutputFile" -ForegroundColor Green
Write-Host "Analysis complete! (Used cached metadata from $($metadataCache.extractionDate))" -ForegroundColor Green
